cmake_minimum_required(VERSION 3.16)
project(ArrowKey VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Gui)

# Enable Qt moc, uic, rcc
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Add executable
add_executable(<PERSON><PERSON><PERSON>
    main.cpp
    mainwindow.cpp
    mainwindow.h
    mainwindow.ui
    settingsdialog.cpp
    settingsdialog.h
)

# Link Qt6 libraries
target_link_libraries(ArrowKey
    Qt6::Core
    Qt6::Widgets
    Qt6::Gui
)

# Add compile definitions
target_compile_definitions(ArrowKey PRIVATE QT_DISABLE_DEPRECATED_BEFORE=0x060000)

# Install target
install(TARGETS <PERSON>Key
    RUNTIME DESTINATION bin
)

#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QTime>
#include <QDebug>
#include <stdexcept>
#include <iostream>

using namespace std;

MainWindow::MainWindow(QWidget *parent) : QMainWindow(parent) , ui(new Ui::MainWindow)
{
    qDebug() << "MainWindow constructor started";
    ui->setupUi(this);
    checkPlatformSupport();
    loadSettings();
    setupDVD();

    // 時刻表示用のラベルを作成（タイマーとコメントを一つのラベルに統合）
    timeLabel = new QLabel(this);
    timeLabel->setAlignment(Qt::AlignCenter);
    timeLabel->setContentsMargins(0, 0, 0, 0);
    setCentralWidget(timeLabel);

    // 初期フォントサイズを設定
    updateFontSize();

    // 時刻更新用タイマー
    displayTimer = new QTimer(this);
    connect(displayTimer, &QTimer::timeout, this, &MainWindow::updateTimeDisplay);
    displayTimer->start(1000); // 1秒ごとに更新
    updateTimeDisplay(); // 初期表示

    // DVDモード用タイマー
    dvdTimer = new QTimer(this);
    dvdTimer->setSingleShot(false);
    dvdTimer->start(16); // 約60FPSで更新（16ms間隔）
    connect(dvdTimer, &QTimer::timeout, this, &MainWindow::moveDVD);

    // フォーカスを設定してキーイベントを受け取れるようにする
    setFocusPolicy(Qt::StrongFocus);
    setFocus();

    // ウィンドウがアクティブになったときにフォーカスを取得
    activateWindow();
    raise();

    qDebug() << "MainWindow constructor completed";
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::setupDVD()
{
   vert = UP;
   hor = LEFT;
   QRect screenGeometry = screen->geometry();
   // シード値を設定
   srand(static_cast<unsigned int>(time(nullptr)));
   iconX = rand() % (screenGeometry.width() - width()) + 1;
   iconY = rand() % (screenGeometry.height() - height()) + 1;

   // DVDスクリーンセーバーのような速度を設定（設定可能な範囲）
   velocityX = generateRandomSpeed();
   velocityY = generateRandomSpeed();
}

void MainWindow::updateTimeDisplay()
{
    int hours = elapsedSeconds / 3600;
    int minutes = (elapsedSeconds % 3600) / 60;
    int seconds = elapsedSeconds % 60;

    QString timeString = QString("%1:%2:%3")
        .arg(hours, 2, 10, QChar('0'))
        .arg(minutes, 2, 10, QChar('0'))
        .arg(seconds, 2, 10, QChar('0'));

    // タイマーとコメントを一つのラベルに表示（コメントは少し小さく）
    int fontSize = isFullScreen() ? fullscreenFontSize : normalFontSize;
    int commentFontSize = fontSize * 0.8; // コメントは80%のサイズ

    QString htmlText = QString("<div style='font-size: %1px; font-weight: bold;'>%2 at %3</div>"
                              "<div style='font-size: %4px; font-weight: bold;'>%5</div>")
                              .arg(fontSize)
                              .arg(customText)
                              .arg(timeString)
                              .arg(commentFontSize)
                              .arg(comment);

    timeLabel->setText(htmlText);

    elapsedSeconds++;
}

void MainWindow::moveDVD()
{
   if (!canMoveWindow || !dvdModeEnabled) return;
   QRect screenGeometry = screen->geometry();

   // 次の位置を計算
   double nextX = iconX + velocityX;
   double nextY = iconY + velocityY;

   // 境界判定と跳ね返り
   bool hitHorizontal = false;
   bool hitVertical = false;

   // 水平方向の境界判定
   if (nextX <= 0) {
       nextX = 0;
       hitHorizontal = true;
   } else if (nextX >= screenGeometry.width() - width()) {
       nextX = screenGeometry.width() - width();
       hitHorizontal = true;
   }

   // 垂直方向の境界判定
   if (nextY <= 0) {
       nextY = 0;
       hitVertical = true;
   } else if (nextY >= screenGeometry.height() - height()) {
       nextY = screenGeometry.height() - height();
       hitVertical = true;
   }

   // 跳ね返り時の速度変更
   if (hitHorizontal) {
       velocityX = (nextX <= 0) ? generateRandomSpeed() : -generateRandomSpeed();
   }
   if (hitVertical) {
       velocityY = (nextY <= 0) ? generateRandomSpeed() : -generateRandomSpeed();
   }

   // 位置を更新
   iconX = nextX;
   iconY = nextY;

   // ウィンドウを移動（整数値に変換）
   move(static_cast<int>(iconX), static_cast<int>(iconY));
}




void MainWindow::checkPlatformSupport()
{
    QString platformName = QGuiApplication::platformName();

    if (platformName == "wayland") {
        canMoveWindow = false;
        QMessageBox::warning(this, "Platform Warning",
            "Running on Wayland. Window positioning may not work properly.\n"
            "Consider running with: QT_QPA_PLATFORM=xcb ./ArrowKey\n"
            "or use X11 session for full functionality.");
    } else if (platformName == "xcb" || platformName == "x11") {
        canMoveWindow = true;
    } else {
        // 他のプラットフォームでは試してみる
        canMoveWindow = true;
    }
}

void MainWindow::contextMenuEvent(QContextMenuEvent *event)
{
    QMenu contextMenu(this);

    QAction *changeTextAction = new QAction("テキストを変更 (T)", this);
    connect(changeTextAction, &QAction::triggered, this, &MainWindow::showCustomTextDialog);
    contextMenu.addAction(changeTextAction);

    QAction *changeCommentAction = new QAction("コメントを変更 (C)", this);
    connect(changeCommentAction, &QAction::triggered, this, &MainWindow::showCommentDialog);
    contextMenu.addAction(changeCommentAction);

    QAction *resetTimerAction = new QAction("タイマーをリセット (R)", this);
    connect(resetTimerAction, &QAction::triggered, this, &MainWindow::resetTimer);
    contextMenu.addAction(resetTimerAction);

    contextMenu.addSeparator();

    QAction *toggleDVDAction = new QAction(dvdModeEnabled ? "DVDモードを無効にする (D)" : "DVDモードを有効にする (D)", this);
    connect(toggleDVDAction, &QAction::triggered, this, &MainWindow::toggleDVDMode);
    contextMenu.addAction(toggleDVDAction);

    QAction *toggleFullscreenAction = new QAction(isFullScreen() ? "ウィンドウモードに戻す (F)" : "フルスクリーンにする (F)", this);
    connect(toggleFullscreenAction, &QAction::triggered, this, &MainWindow::toggleFullscreen);
    contextMenu.addAction(toggleFullscreenAction);

    contextMenu.addSeparator();

    QAction *settingsAction = new QAction("DVD速度設定 (S)", this);
    connect(settingsAction, &QAction::triggered, this, &MainWindow::showSettingsDialog);
    contextMenu.addAction(settingsAction);

    contextMenu.exec(event->globalPos());
}

void MainWindow::showCustomTextDialog()
{
    try {
        bool ok;
        QString text = QInputDialog::getText(this, "カスタムテキスト",
                                             "表示するテキストを入力してください:",
                                             QLineEdit::Normal,
                                             customText, &ok);
        if (ok && !text.isEmpty()) {
            customText = text;
            saveSettings();
            updateTimeDisplay(); // 表示を即座に更新
        }
    } catch (...) {
        // エラーが発生した場合は何もしない
    }
}

void MainWindow::keyPressEvent(QKeyEvent *event)
{
    qDebug() << "keyPressEvent called";
    std::cout << "keyPressEvent called" << std::endl;

    // nullチェック
    if (!event) {
        qDebug() << "Event is null, calling parent";
        std::cout << "Event is null, calling parent" << std::endl;
        QMainWindow::keyPressEvent(event);
        return;
    }

    // 修飾キーのみが押された場合は何もしない
    int key = event->key();

    // デバッグ情報（常に出力）
    qDebug() << "Key pressed:" << key << "Modifiers:" << event->modifiers();
    std::cout << "Key pressed: " << key << " Modifiers: " << event->modifiers() << std::endl;

    if (key == Qt::Key_Shift || key == Qt::Key_Control || key == Qt::Key_Alt ||
        key == Qt::Key_Meta || key == Qt::Key_AltGr || key == Qt::Key_CapsLock ||
        key == Qt::Key_NumLock || key == Qt::Key_ScrollLock) {
        qDebug() << "Modifier key, calling parent";
        std::cout << "Modifier key, calling parent" << std::endl;
        QMainWindow::keyPressEvent(event);
        return;
    }

    // 修飾キーが押されている場合は通常の処理をスキップ
    Qt::KeyboardModifiers modifiers = event->modifiers();
    if (modifiers & (Qt::ControlModifier | Qt::AltModifier | Qt::MetaModifier)) {
        qDebug() << "Modifier combination, calling parent";
        std::cout << "Modifier combination, calling parent" << std::endl;
        QMainWindow::keyPressEvent(event);
        return;
    }

    qDebug() << "Processing key:" << key;
    std::cout << "Processing key: " << key << std::endl;

    // 通常のキー処理（修飾キーなしまたはShiftのみ）
    switch (key) {
    case Qt::Key_S:
        qDebug() << "Showing settings dialog";
        std::cout << "Showing settings dialog" << std::endl;
        showSettingsDialog();
        break;
    default:
        qDebug() << "Unhandled key, calling parent";
        std::cout << "Unhandled key, calling parent" << std::endl;
        QMainWindow::keyPressEvent(event);
        break;
    }

    qDebug() << "keyPressEvent completed successfully";
    std::cout << "keyPressEvent completed successfully" << std::endl;
}

void MainWindow::toggleDVDMode()
{
    try {
        dvdModeEnabled = !dvdModeEnabled;
        if (dvdModeEnabled) {
            // DVDモードを有効にする
            setupDVD(); // 位置と速度をリセット
            if (dvdTimer) {
                dvdTimer->start(16);
            }
        } else {
            // DVDモードを無効にする
            if (dvdTimer) {
                dvdTimer->stop();
            }
        }
    } catch (...) {
        // エラーが発生した場合は何もしない
    }
}

void MainWindow::toggleFullscreen()
{
    try {
        if (isFullScreen()) {
            // ウィンドウモードに戻す
            showNormal();
            // 元のサイズ制約を復元
            setMinimumSize(280, 150);
            setMaximumSize(280, 150);
            isFullscreen = false;
        } else {
            // フルスクリーンモードにする
            // サイズ制約を解除
            setMinimumSize(0, 0);
            setMaximumSize(16777215, 16777215); // Qt's maximum widget size
            showFullScreen();
            isFullscreen = true;
        }

        // フォントサイズを更新
        updateFontSize();
    } catch (...) {
        // エラーが発生した場合は何もしない
    }
}

void MainWindow::updateFontSize()
{
    int fontSize = isFullScreen() ? fullscreenFontSize : normalFontSize;
    QString labelStyleSheet = QString("QLabel { "
                                    "font-size: %1px; "
                                    "font-weight: bold; "
                                    "margin: 0px; "
                                    "padding: 0px; "
                                    "border: 0px; "
                                    "}").arg(fontSize);

    timeLabel->setStyleSheet(labelStyleSheet);
}

void MainWindow::resetTimer()
{
    try {
        elapsedSeconds = 0;
        updateTimeDisplay(); // 表示を即座に更新
    } catch (...) {
        // エラーが発生した場合は何もしない
    }
}

void MainWindow::showCommentDialog()
{
    try {
        bool ok;
        QString text = QInputDialog::getText(this, "コメント",
                                             "コメントを入力してください:",
                                             QLineEdit::Normal,
                                             comment, &ok);
        if (ok) {
            comment = text;
            saveSettings();
            updateTimeDisplay(); // 表示を即座に更新
        }
    } catch (...) {
        // エラーが発生した場合は何もしない
    }
}

void MainWindow::showSettingsDialog()
{
    try {
        SettingsDialog dialog(this);
        dialog.setMinSpeed(currentMinSpeed);
        dialog.setMaxSpeed(currentMaxSpeed);

        if (dialog.exec() == QDialog::Accepted) {
            currentMinSpeed = dialog.getMinSpeed();
            currentMaxSpeed = dialog.getMaxSpeed();
            saveSettings();

            // DVDモードが有効な場合は速度を再設定
            if (dvdModeEnabled) {
                setupDVD();
            }
        }
    } catch (...) {
        // エラーが発生した場合は何もしない
    }
}

void MainWindow::saveSettings()
{
    QSettings settings("ArrowKey", "DVDTimer");
    settings.setValue("dvd/minSpeed", currentMinSpeed);
    settings.setValue("dvd/maxSpeed", currentMaxSpeed);
    settings.setValue("ui/customText", customText);
    settings.setValue("ui/comment", comment);
    settings.setValue("ui/normalFontSize", normalFontSize);
    settings.setValue("ui/fullscreenFontSize", fullscreenFontSize);
}

void MainWindow::loadSettings()
{
    QSettings settings("ArrowKey", "DVDTimer");
    currentMinSpeed = settings.value("dvd/minSpeed", 2).toInt();
    currentMaxSpeed = settings.value("dvd/maxSpeed", 4).toInt();
    customText = settings.value("ui/customText", "Nothing to do").toString();
    comment = settings.value("ui/comment", "...").toString();
    normalFontSize = settings.value("ui/normalFontSize", 18).toInt();
    fullscreenFontSize = settings.value("ui/fullscreenFontSize", 72).toInt();
}

double MainWindow::generateRandomSpeed()
{
    int speedRange = currentMaxSpeed - currentMinSpeed + 1;
    double baseSpeed = static_cast<double>(rand() % speedRange + currentMinSpeed);

    // 少しランダムな変動を加える（±10%）
    double variation = (rand() % 21 - 10) / 100.0; // -0.1 to 0.1
    baseSpeed *= (1.0 + variation);

    // 最小値を保証
    if (baseSpeed < 1.0) baseSpeed = 1.0;

    return baseSpeed;
}



#include "settingsdialog.h"

SettingsDialog::SettingsDialog(QWidget *parent)
    : QDialog(parent)
{
    setWindowTitle("DVD速度設定");
    setModal(true);
    setFixedSize(400, 300);
    
    setupUI();
    
    // シグナル・スロット接続
    connect(minSpeedSlider, &QSlider::valueChanged, minSpeedSpinBox, &QSpinBox::setValue);
    connect(minSpeedSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), minSpeedSlider, &QSlider::setValue);
    connect(minSpeedSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &SettingsDialog::onMinSpeedChanged);
    
    connect(maxSpeedSlider, &QSlider::valueChanged, maxSpeedSpinBox, &QSpinBox::setValue);
    connect(maxSpeedSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), maxSpeedSlider, &QSlider::setValue);
    connect(maxSpeedSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &SettingsDialog::onMaxSpeedChanged);
    
    connect(resetButton, &QPushButton::clicked, this, &SettingsDialog::resetToDefaults);
    connect(okButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);
}

void SettingsDialog::setupUI()
{
    mainLayout = new QVBoxLayout(this);
    
    // 速度設定グループ
    speedGroupBox = new QGroupBox("DVD移動速度設定", this);
    QVBoxLayout *speedLayout = new QVBoxLayout(speedGroupBox);
    
    // 最小速度設定
    minSpeedLabel = new QLabel("最小速度 (ピクセル/フレーム):", this);
    speedLayout->addWidget(minSpeedLabel);
    
    QHBoxLayout *minSpeedHLayout = new QHBoxLayout();
    minSpeedSlider = new QSlider(Qt::Horizontal, this);
    minSpeedSlider->setRange(MIN_SPEED_LIMIT, MAX_SPEED_LIMIT);
    minSpeedSlider->setValue(DEFAULT_MIN_SPEED);
    
    minSpeedSpinBox = new QSpinBox(this);
    minSpeedSpinBox->setRange(MIN_SPEED_LIMIT, MAX_SPEED_LIMIT);
    minSpeedSpinBox->setValue(DEFAULT_MIN_SPEED);
    minSpeedSpinBox->setSuffix(" px");
    
    minSpeedHLayout->addWidget(minSpeedSlider);
    minSpeedHLayout->addWidget(minSpeedSpinBox);
    speedLayout->addLayout(minSpeedHLayout);
    
    // 最大速度設定
    maxSpeedLabel = new QLabel("最大速度 (ピクセル/フレーム):", this);
    speedLayout->addWidget(maxSpeedLabel);
    
    QHBoxLayout *maxSpeedHLayout = new QHBoxLayout();
    maxSpeedSlider = new QSlider(Qt::Horizontal, this);
    maxSpeedSlider->setRange(MIN_SPEED_LIMIT, MAX_SPEED_LIMIT);
    maxSpeedSlider->setValue(DEFAULT_MAX_SPEED);
    
    maxSpeedSpinBox = new QSpinBox(this);
    maxSpeedSpinBox->setRange(MIN_SPEED_LIMIT, MAX_SPEED_LIMIT);
    maxSpeedSpinBox->setValue(DEFAULT_MAX_SPEED);
    maxSpeedSpinBox->setSuffix(" px");
    
    maxSpeedHLayout->addWidget(maxSpeedSlider);
    maxSpeedHLayout->addWidget(maxSpeedSpinBox);
    speedLayout->addLayout(maxSpeedHLayout);
    
    // 説明ラベル
    QLabel *descLabel = new QLabel("DVDスクリーンセーバーのような動きの速度範囲を設定します。\n"
                                  "実際の速度はこの範囲内でランダムに決定されます。", this);
    descLabel->setWordWrap(true);
    descLabel->setStyleSheet("color: gray; font-size: 11px;");
    speedLayout->addWidget(descLabel);
    
    mainLayout->addWidget(speedGroupBox);
    
    // ボタン
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    
    resetButton = new QPushButton("デフォルトに戻す", this);
    okButton = new QPushButton("OK", this);
    cancelButton = new QPushButton("キャンセル", this);
    
    buttonLayout->addWidget(resetButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(okButton);
    buttonLayout->addWidget(cancelButton);
    
    mainLayout->addLayout(buttonLayout);
}

void SettingsDialog::onMinSpeedChanged(int value)
{
    // 最小速度が最大速度より大きくならないようにする
    if (value > maxSpeedSpinBox->value()) {
        maxSpeedSpinBox->setValue(value);
    }
}

void SettingsDialog::onMaxSpeedChanged(int value)
{
    // 最大速度が最小速度より小さくならないようにする
    if (value < minSpeedSpinBox->value()) {
        minSpeedSpinBox->setValue(value);
    }
}

void SettingsDialog::resetToDefaults()
{
    minSpeedSpinBox->setValue(DEFAULT_MIN_SPEED);
    maxSpeedSpinBox->setValue(DEFAULT_MAX_SPEED);
}

#ifndef SETTINGSDIALOG_H
#define SETTINGSDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QSlider>
#include <QSpinBox>
#include <QPushButton>
#include <QGroupBox>

class SettingsDialog : public QDialog
{
    Q_OBJECT

public:
    explicit SettingsDialog(QWidget *parent = nullptr);
    
    // 速度設定の取得・設定
    int getMinSpeed() const { return minSpeedSpinBox->value(); }
    int getMaxSpeed() const { return maxSpeedSpinBox->value(); }
    void setMinSpeed(int speed) { minSpeedSpinBox->setValue(speed); }
    void setMaxSpeed(int speed) { maxSpeedSpinBox->setValue(speed); }

private slots:
    void onMinSpeedChanged(int value);
    void onMaxSpeedChanged(int value);
    void resetToDefaults();
    void setSlowPreset();
    void setNormalPreset();
    void setFastPreset();
    void setVeryFastPreset();
    void setExtremePreset();

private:
    void setupUI();
    void updateSliders();
    
    QVBoxLayout *mainLayout;
    QGroupBox *speedGroupBox;
    
    QLabel *minSpeedLabel;
    QSlider *minSpeedSlider;
    QSpinBox *minSpeedSpinBox;
    
    QLabel *maxSpeedLabel;
    QSlider *maxSpeedSlider;
    QSpinBox *maxSpeedSpinBox;

    // プリセットボタン
    QPushButton *slowButton;
    QPushButton *normalButton;
    QPushButton *fastButton;
    QPushButton *veryFastButton;
    QPushButton *extremeButton;

    QPushButton *resetButton;
    QPushButton *okButton;
    QPushButton *cancelButton;
    
    static const int MIN_SPEED_LIMIT = 1;
    static const int MAX_SPEED_LIMIT = 100;
    static const int DEFAULT_MIN_SPEED = 2;
    static const int DEFAULT_MAX_SPEED = 4;
};

#endif // SETTINGSDIALOG_H

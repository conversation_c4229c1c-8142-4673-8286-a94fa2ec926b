#include "settingsdialog.h"

SettingsDialog::SettingsDialog(QWidget *parent)
    : QDialog(parent)
{
    setWindowTitle("DVD速度設定");
    setModal(true);
    setFixedSize(520, 450);
    
    setupUI();
    
    // シグナル・スロット接続
    connect(minSpeedSlider, &QSlider::valueChanged, minSpeedSpinBox, &QSpinBox::setValue);
    connect(minSpeedSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), minSpeedSlider, &QSlider::setValue);
    connect(minSpeedSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &SettingsDialog::onMinSpeedChanged);
    
    connect(maxSpeedSlider, &QSlider::valueChanged, maxSpeedSpinBox, &QSpinBox::setValue);
    connect(maxSpeedSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), maxSpeedSlider, &QSlider::setValue);
    connect(maxSpeedSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &SettingsDialog::onMaxSpeedChanged);
    
    connect(resetButton, &QPushButton::clicked, this, &SettingsDialog::resetToDefaults);
    connect(okButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);

    // プリセットボタンの接続
    connect(slowButton, &QPushButton::clicked, this, &SettingsDialog::setSlowPreset);
    connect(normalButton, &QPushButton::clicked, this, &SettingsDialog::setNormalPreset);
    connect(fastButton, &QPushButton::clicked, this, &SettingsDialog::setFastPreset);
    connect(veryFastButton, &QPushButton::clicked, this, &SettingsDialog::setVeryFastPreset);
    connect(extremeButton, &QPushButton::clicked, this, &SettingsDialog::setExtremePreset);
}

void SettingsDialog::setupUI()
{
    mainLayout = new QVBoxLayout(this);
    
    // 速度設定グループ
    speedGroupBox = new QGroupBox("DVD移動速度設定", this);
    QVBoxLayout *speedLayout = new QVBoxLayout(speedGroupBox);
    
    // 最小速度設定
    minSpeedLabel = new QLabel("最小速度 (ピクセル/フレーム):", this);
    speedLayout->addWidget(minSpeedLabel);
    
    QHBoxLayout *minSpeedHLayout = new QHBoxLayout();
    minSpeedSlider = new QSlider(Qt::Horizontal, this);
    minSpeedSlider->setRange(MIN_SPEED_LIMIT, MAX_SPEED_LIMIT);
    minSpeedSlider->setValue(DEFAULT_MIN_SPEED);
    minSpeedSlider->setTickPosition(QSlider::TicksBelow);
    minSpeedSlider->setTickInterval(10);

    minSpeedSpinBox = new QSpinBox(this);
    minSpeedSpinBox->setRange(MIN_SPEED_LIMIT, MAX_SPEED_LIMIT);
    minSpeedSpinBox->setValue(DEFAULT_MIN_SPEED);
    minSpeedSpinBox->setSuffix(" px");
    minSpeedSpinBox->setMinimumWidth(80);
    
    minSpeedHLayout->addWidget(minSpeedSlider);
    minSpeedHLayout->addWidget(minSpeedSpinBox);
    speedLayout->addLayout(minSpeedHLayout);
    
    // 最大速度設定
    maxSpeedLabel = new QLabel("最大速度 (ピクセル/フレーム):", this);
    speedLayout->addWidget(maxSpeedLabel);
    
    QHBoxLayout *maxSpeedHLayout = new QHBoxLayout();
    maxSpeedSlider = new QSlider(Qt::Horizontal, this);
    maxSpeedSlider->setRange(MIN_SPEED_LIMIT, MAX_SPEED_LIMIT);
    maxSpeedSlider->setValue(DEFAULT_MAX_SPEED);
    maxSpeedSlider->setTickPosition(QSlider::TicksBelow);
    maxSpeedSlider->setTickInterval(10);

    maxSpeedSpinBox = new QSpinBox(this);
    maxSpeedSpinBox->setRange(MIN_SPEED_LIMIT, MAX_SPEED_LIMIT);
    maxSpeedSpinBox->setValue(DEFAULT_MAX_SPEED);
    maxSpeedSpinBox->setSuffix(" px");
    maxSpeedSpinBox->setMinimumWidth(80);
    
    maxSpeedHLayout->addWidget(maxSpeedSlider);
    maxSpeedHLayout->addWidget(maxSpeedSpinBox);
    speedLayout->addLayout(maxSpeedHLayout);

    // プリセットボタン
    QLabel *presetLabel = new QLabel("クイック設定:", this);
    speedLayout->addWidget(presetLabel);

    QHBoxLayout *presetLayout1 = new QHBoxLayout();
    slowButton = new QPushButton("ゆっくり (1-3)", this);
    normalButton = new QPushButton("普通 (2-4)", this);
    fastButton = new QPushButton("速い (5-15)", this);

    slowButton->setStyleSheet("QPushButton { background-color: #e8f5e8; }");
    normalButton->setStyleSheet("QPushButton { background-color: #e8f0ff; }");
    fastButton->setStyleSheet("QPushButton { background-color: #fff0e8; }");

    presetLayout1->addWidget(slowButton);
    presetLayout1->addWidget(normalButton);
    presetLayout1->addWidget(fastButton);
    speedLayout->addLayout(presetLayout1);

    QHBoxLayout *presetLayout2 = new QHBoxLayout();
    veryFastButton = new QPushButton("超高速 (20-50)", this);
    extremeButton = new QPushButton("極速 (50-100)", this);

    veryFastButton->setStyleSheet("QPushButton { background-color: #ffe8e8; }");
    extremeButton->setStyleSheet("QPushButton { background-color: #ff8888; color: white; font-weight: bold; }");

    presetLayout2->addWidget(veryFastButton);
    presetLayout2->addWidget(extremeButton);
    speedLayout->addLayout(presetLayout2);

    // 説明ラベル
    QLabel *descLabel = new QLabel("DVDスクリーンセーバーのような動きの速度範囲を設定します。\n"
                                  "実際の速度はこの範囲内でランダムに決定されます。\n"
                                  "最大100ピクセル/フレームまで設定可能です。", this);
    descLabel->setWordWrap(true);
    descLabel->setStyleSheet("color: gray; font-size: 11px;");
    speedLayout->addWidget(descLabel);
    
    mainLayout->addWidget(speedGroupBox);
    
    // ボタン
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    
    resetButton = new QPushButton("デフォルトに戻す", this);
    okButton = new QPushButton("OK", this);
    cancelButton = new QPushButton("キャンセル", this);
    
    buttonLayout->addWidget(resetButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(okButton);
    buttonLayout->addWidget(cancelButton);
    
    mainLayout->addLayout(buttonLayout);
}

void SettingsDialog::onMinSpeedChanged(int value)
{
    // 最小速度が最大速度より大きくならないようにする
    if (value > maxSpeedSpinBox->value()) {
        maxSpeedSpinBox->setValue(value);
    }
}

void SettingsDialog::onMaxSpeedChanged(int value)
{
    // 最大速度が最小速度より小さくならないようにする
    if (value < minSpeedSpinBox->value()) {
        minSpeedSpinBox->setValue(value);
    }
}

void SettingsDialog::resetToDefaults()
{
    minSpeedSpinBox->setValue(DEFAULT_MIN_SPEED);
    maxSpeedSpinBox->setValue(DEFAULT_MAX_SPEED);
}

void SettingsDialog::setSlowPreset()
{
    minSpeedSpinBox->setValue(1);
    maxSpeedSpinBox->setValue(3);
}

void SettingsDialog::setNormalPreset()
{
    minSpeedSpinBox->setValue(2);
    maxSpeedSpinBox->setValue(4);
}

void SettingsDialog::setFastPreset()
{
    minSpeedSpinBox->setValue(5);
    maxSpeedSpinBox->setValue(15);
}

void SettingsDialog::setVeryFastPreset()
{
    minSpeedSpinBox->setValue(20);
    maxSpeedSpinBox->setValue(50);
}

void SettingsDialog::setExtremePreset()
{
    minSpeedSpinBox->setValue(50);
    maxSpeedSpinBox->setValue(100);
}
